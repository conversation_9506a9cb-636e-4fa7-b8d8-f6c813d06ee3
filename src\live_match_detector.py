"""
Live Match Detector for Valorant.
Automatically detects when the user is in a live match and extracts player information.
"""

import time
import threading
from typing import Optional, Dict, List, Any, Callable

try:
    from .valorant_client import ValorantLocalClient, is_valorant_running
    from .api_client import <PERSON>orantAP<PERSON>
except ImportError:
    # Handle relative imports when running as script
    from valorant_client import ValorantLocalClient, is_valorant_running
    from api_client import ValorantAPI


class LiveMatchDetector:
    """Detects and monitors live Valorant matches."""
    
    def __init__(self, api_client: ValorantAPI):
        self.api_client = api_client
        self.client = ValorantLocalClient()
        self.is_monitoring = False
        self.current_match_data = None
        self.last_game_state = None
        self.callbacks = []
        self.monitor_thread = None
        
    def add_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Add a callback function to be called when match data is updated."""
        self.callbacks.append(callback)
        
    def remove_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Remove a callback function."""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
            
    def _notify_callbacks(self, match_data: Dict[str, Any]):
        """Notify all registered callbacks with new match data."""
        for callback in self.callbacks:
            try:
                callback(match_data)
            except Exception as e:
                print(f"Error in callback: {e}")
    
    def start_monitoring(self) -> bool:
        """
        Start monitoring for live matches.
        
        Returns:
            True if monitoring started successfully, False otherwise
        """
        if self.is_monitoring:
            return True
            
        if not is_valorant_running():
            return False
            
        if not self.client.connect():
            return False
            
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        return True
    
    def stop_monitoring(self):
        """Stop monitoring for live matches."""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
            
    def _monitor_loop(self):
        """Main monitoring loop that runs in a separate thread."""
        while self.is_monitoring:
            try:
                if not is_valorant_running():
                    self.is_monitoring = False
                    break
                    
                if not self.client.is_connected():
                    if not self.client.connect():
                        time.sleep(5)
                        continue
                
                # Check current game state
                game_state = self.client.get_current_game_state()
                
                if game_state != self.last_game_state:
                    self.last_game_state = game_state
                    print(f"Game state changed to: {game_state}")
                
                # Handle different game states
                if game_state == 'PREGAME':
                    self._handle_pregame()
                elif game_state == 'INGAME':
                    self._handle_ingame()
                elif game_state == 'connected':
                    # Handle connected state (deathmatch, etc.)
                    self._handle_connected()
                elif game_state == 'MENUS':
                    if self.current_match_data:
                        self.current_match_data = None
                        self._notify_callbacks({'state': 'MENUS', 'players': []})
                else:
                    # Log unknown game states for debugging
                    print(f"Unknown game state detected: {game_state}")
                
                time.sleep(2)  # Check every 2 seconds
                
            except Exception as e:
                print(f"Error in monitor loop: {e}")
                time.sleep(5)
    
    def _handle_pregame(self):
        """Handle pregame state - extract player information."""
        try:
            pregame_data = self.client.get_pregame_match()
            if not pregame_data:
                return
                
            players = self._extract_player_info(pregame_data)
            if players:
                match_data = {
                    'state': 'PREGAME',
                    'players': players,
                    'map': pregame_data.get('MapID', 'Unknown'),
                    'mode': pregame_data.get('Mode', 'Unknown')
                }
                
                if self.current_match_data != match_data:
                    self.current_match_data = match_data
                    self._notify_callbacks(match_data)
                    
        except Exception as e:
            print(f"Error handling pregame: {e}")
    
    def _handle_ingame(self):
        """Handle in-game state - extract player information."""
        try:
            match_data = self.client.get_current_match()
            if not match_data:
                return
                
            players = self._extract_player_info(match_data)
            if players:
                game_data = {
                    'state': 'INGAME',
                    'players': players,
                    'map': match_data.get('MapID', 'Unknown'),
                    'mode': match_data.get('Mode', 'Unknown')
                }
                
                if self.current_match_data != game_data:
                    self.current_match_data = game_data
                    self._notify_callbacks(game_data)
                    
        except Exception as e:
            print(f"Error handling ingame: {e}")

    def _handle_connected(self):
        """Handle connected state - for deathmatch, swiftplay and other game modes."""
        try:
            print("Trying to get match data in connected state...")

            # Try current match endpoint first
            match_data = self.client.get_current_match()
            print(f"Current match data: {match_data}")

            # Try pregame endpoint
            if not match_data:
                print("No current match data, trying pregame...")
                match_data = self.client.get_pregame_match()
                print(f"Pregame match data: {match_data}")

            # Try to get game state info
            game_state_info = self.client.get_current_game_state()
            print(f"Game state info: {game_state_info}")

            # If no match data, determine the actual game mode from presence/session data
            if not match_data:
                print("No standard match data found, checking game mode from presence...")

                # Get actual game mode from session/presence data
                game_mode_info = self._get_game_mode_from_presence()

                if game_mode_info:
                    mode = game_mode_info.get('mode', 'Unknown')
                    queue_id = game_mode_info.get('queue_id', '')

                    print(f"Detected game mode: {mode} (queue_id: {queue_id})")

                    # Check if this is actually deathmatch or another mode
                    if self._is_deathmatch_mode(mode, queue_id):
                        print("Confirmed deathmatch mode - no player data available")

                        deathmatch_data = {
                            'state': 'DEATHMATCH',
                            'mode': 'Deathmatch',
                            'message': 'Deathmatch detected - Player analysis not available for this mode',
                            'players': [],
                            'map': 'Unknown'
                        }

                        if self.current_match_data != deathmatch_data:
                            self.current_match_data = deathmatch_data
                            self._notify_callbacks(deathmatch_data)
                            print("Deathmatch mode detected and reported")
                        return
                    else:
                        print(f"Detected {mode} - API doesn't provide player data for this mode")

                        # Create connected state data for Swiftplay or other modes
                        connected_data = {
                            'state': 'CONNECTED',
                            'mode': mode,
                            'message': f'{mode} detected - Player data not available via local API',
                            'players': [],
                            'map': 'Unknown'
                        }

                        if self.current_match_data != connected_data:
                            self.current_match_data = connected_data
                            self._notify_callbacks(connected_data)
                            print(f"{mode} mode detected and reported")
                        return

                # Fallback if we can't determine the mode
                print("Could not determine game mode - defaulting to unknown connected state")

                fallback_data = {
                    'state': 'CONNECTED',
                    'mode': 'Unknown',
                    'message': 'Connected to match - Player data not available',
                    'players': [],
                    'map': 'Unknown'
                }

                if self.current_match_data != fallback_data:
                    self.current_match_data = fallback_data
                    self._notify_callbacks(fallback_data)
                    print("Unknown connected mode detected and reported")
                return

            print(f"Connected state match data: {match_data}")

            players = self._extract_player_info(match_data)
            if players:
                game_data = {
                    'state': 'CONNECTED',
                    'players': players,
                    'map': match_data.get('MapID', 'Unknown'),
                    'mode': match_data.get('Mode', 'Connected')
                }

                if self.current_match_data != game_data:
                    self.current_match_data = game_data
                    self._notify_callbacks(game_data)
                    print(f"Found {len(players)} players in connected state")
            else:
                print("No players found in connected state")

        except Exception as e:
            print(f"Error handling connected state: {e}")

    def _get_game_mode_from_presence(self) -> Optional[Dict[str, Any]]:
        """
        Get game mode information from presence/session data.

        Returns:
            Dictionary with mode and queue_id information, or None if not found
        """
        try:
            # Try to get session info which might contain queue information
            session_data = self.client.get_session_info()
            if session_data:
                print(f"Session data found: {session_data}")
                # Session data might contain queue or mode information
                # This is a simplified approach - you might need to adjust based on actual API response

            # Try to get party info which might contain queue information
            party_data = self.client.get_party_info()
            if party_data:
                print(f"Party data found: {party_data}")
                # Look for queue ID or mode information in party data
                # This would need to be implemented based on actual Valorant API response structure

            # For now, return None as we need to see the actual API responses
            # to implement proper mode detection
            return None

        except Exception as e:
            print(f"Error getting game mode from presence: {e}")
            return None

    def _is_deathmatch_mode(self, mode: str, queue_id: str) -> bool:
        """
        Determine if the given mode/queue_id represents a deathmatch.

        Args:
            mode: Game mode name
            queue_id: Queue identifier

        Returns:
            True if this is deathmatch, False otherwise
        """
        # Common deathmatch identifiers
        deathmatch_modes = ['deathmatch', 'dm']
        deathmatch_queue_ids = ['deathmatch', 'dm']  # Add actual queue IDs as we discover them

        if mode and mode.lower() in deathmatch_modes:
            return True

        if queue_id and queue_id.lower() in deathmatch_queue_ids:
            return True

        return False

    def _try_alternative_player_detection(self) -> Optional[Dict[str, Any]]:
        """
        Try alternative methods to detect players in Swiftplay and other modes.

        Returns:
            Dictionary with player data and mode info, or None if not found
        """
        try:
            # Method 1: Try to get party members and recent players
            party_data = self.client.get_party_info()
            if party_data and 'Members' in party_data:
                print("Found party data, extracting player info...")

                players = []
                for member in party_data.get('Members', []):
                    player_uuid = member.get('Subject', '')
                    if player_uuid:
                        # Try to get player name from recent matches or other sources
                        player_info = self._get_player_name_from_recent_matches(player_uuid)
                        if player_info:
                            players.append({
                                'uuid': player_uuid,
                                'name': player_info.get('name', 'Unknown'),
                                'tag': player_info.get('tag', 'Unknown'),
                                'rank': 'Unknown',  # Will be fetched later
                                'rr': 0,
                                'team': 'Unknown',
                                'agent': 'Unknown',
                                'level': 0
                            })

                if players:
                    return {
                        'players': players,
                        'mode': 'Swiftplay',
                        'map': 'Unknown'
                    }

            # Method 2: Try to get current session info
            session_data = self.client.get_session_info()
            if session_data:
                print("Found session data, checking for player info...")
                # This might contain player information in some cases

            # Method 3: Check if we can get any player data from chat participants
            # This is a fallback method
            print("No alternative player data found")
            return None

        except Exception as e:
            print(f"Error in alternative player detection: {e}")
            return None

    def _get_player_name_from_recent_matches(self, player_uuid: str) -> Optional[Dict[str, Any]]:
        """
        Try to get player name from recent match history or other sources.

        Args:
            player_uuid: Player's UUID

        Returns:
            Player info dictionary or None
        """
        try:
            # This is a placeholder - in a real implementation, you might:
            # 1. Check recent match history for this UUID
            # 2. Use a local cache of UUID to name mappings
            # 3. Try other Valorant API endpoints

            # For now, return None as we don't have a reliable way to get names from UUIDs
            return None
        except Exception:
            return None

    def _extract_player_info(self, match_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract player information from match data and enrich with rank info.
        
        Args:
            match_data: Raw match data from local client
            
        Returns:
            List of player information dictionaries
        """
        players = []
        
        # Extract players from different possible structures
        player_list = []
        if 'AllyTeam' in match_data and 'EnemyTeam' in match_data:
            # Pregame structure
            player_list.extend(match_data.get('AllyTeam', {}).get('Players', []))
            player_list.extend(match_data.get('EnemyTeam', {}).get('Players', []))
        elif 'Players' in match_data:
            # Direct players list
            player_list = match_data.get('Players', [])
        
        for player_data in player_list:
            try:
                player_uuid = player_data.get('Subject', '')
                character_id = player_data.get('CharacterID', '')
                team_id = player_data.get('TeamID', '')
                
                # Try to get player name and tag from the API using UUID
                player_info = self._get_player_info_by_uuid(player_uuid)
                
                if player_info:
                    players.append({
                        'uuid': player_uuid,
                        'name': player_info.get('name', 'Unknown'),
                        'tag': player_info.get('tag', 'Unknown'),
                        'rank': player_info.get('rank', 'Unrated'),
                        'rr': player_info.get('rr', 0),
                        'team': 'Red' if team_id == 'Red' else 'Blue',
                        'agent': self._get_agent_name(character_id),
                        'level': player_info.get('level', 0)
                    })
                else:
                    # Fallback if we can't get player info
                    players.append({
                        'uuid': player_uuid,
                        'name': 'Unknown',
                        'tag': 'Unknown',
                        'rank': 'Unknown',
                        'rr': 0,
                        'team': 'Red' if team_id == 'Red' else 'Blue',
                        'agent': self._get_agent_name(character_id),
                        'level': 0
                    })
                    
            except Exception as e:
                print(f"Error processing player data: {e}")
                continue
                
        return players
    
    def _get_player_info_by_uuid(self, player_uuid: str) -> Optional[Dict[str, Any]]:
        """
        Get player information by UUID using the API.
        
        Args:
            player_uuid: Player's UUID
            
        Returns:
            Player information dictionary or None
        """
        try:
            # This would require a different API endpoint that accepts UUIDs
            # For now, return None as the HenrikDev API primarily uses name#tag
            return None
        except Exception:
            return None
    
    def _get_agent_name(self, character_id: str) -> str:
        """
        Convert character ID to agent name.
        
        Args:
            character_id: Character UUID
            
        Returns:
            Agent name
        """
        # Agent ID to name mapping (partial list)
        agent_map = {
            '5f8d3a7f-467b-97f3-062c-13acf203c006': 'Breach',
            'f94c3b30-42be-e959-889c-5aa313dba261': 'Raze',
            '22697a3d-45bf-8dd7-4fec-84a9e28c69d7': 'Chamber',
            '601dbbe7-43ce-be57-2a40-4abd24953621': 'KAY/O',
            '6f2a04ca-43e0-be17-7f36-b3908627744d': 'Skye',
            '117ed9e3-49f3-6512-3ccf-0cada7e3823b': 'Cypher',
            '320b2a48-4d9b-a075-30f1-1f93a9b638fa': 'Sova',
            '1e58de9c-4950-5125-93e9-a0aee9f98746': 'Killjoy',
            '95b78ed7-4637-86d9-7e41-71ba8c293152': 'Harbor',
            '8e253930-4c05-31dd-1b6c-************': 'Omen',
            '41fb69c1-4189-7b37-f117-bcaf1e96f1bf': 'Astra',
            '9f0d8ba9-4140-b941-57d3-a7ad57c6b417': 'Brimstone',
            'bb2a4828-46eb-8cd1-e765-15848195d751': 'Neon',
            '7f94d92c-4234-0a36-9646-3a87eb8b5c89': 'Yoru',
            'eb93336a-449b-9c1b-0a54-a891f7921d69': 'Phoenix',
            '569fdd95-4d10-43ab-ca70-79becc718b46': 'Sage',
            'a3bfb853-43b2-7238-a4f1-ad90e9e46bcc': 'Reyna',
            'dade69b4-4f5a-8528-247b-219e5a1facd6': 'Fade',
            'e370fa57-4757-3604-3648-499e1f642d3f': 'Gekko',
            '707eab51-4836-f488-046a-cda6bf494859': 'Viper',
            'add6443a-41bd-e414-f6ad-e58d267f4e95': 'Jett',
        }
        
        return agent_map.get(character_id, 'Unknown')
    
    def get_current_match_data(self) -> Optional[Dict[str, Any]]:
        """Get the current match data if available."""
        return self.current_match_data
    
    def is_in_match(self) -> bool:
        """Check if currently in a match."""
        return (self.current_match_data is not None and
                self.current_match_data.get('state') in ['PREGAME', 'INGAME', 'CONNECTED', 'DEATHMATCH'])
